{"private": true, "scripts": {"dev": "vite --host --clearScreen false", "production": "vue-tsc --noEmit && vite build", "tsc": "vue-tsc --noEmit", "lint": "eslint resources/js/**/*.{ts,js,vue}", "lint:fix": "eslint --fix resources/js/**/*.{ts,js,vue}", "test": "vitest run"}, "dependencies": {"@demv_systems/feu-element-plus-styles": "^1.1.0", "@demv_systems/feu-intercom": "^2.1.1", "@demv_systems/feu-tag-manager": "^3.1.0", "@demvsystems/design-components": "10.16.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/pro-duotone-svg-icons": "^6.7.2", "@fortawesome/pro-light-svg-icons": "^6.7.2", "@fortawesome/pro-regular-svg-icons": "^6.7.2", "@fortawesome/pro-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@sentry/vue": "^8.55.0", "@tiptap/core": "^2.26.1", "@tiptap/extension-image": "^2.26.1", "@tiptap/extension-paragraph": "^2.26.1", "@tiptap/pm": "^2.26.1", "@tiptap/suggestion": "^2.26.1", "@tiptap/vue-3": "^2.26.1", "@vueuse/core": "^8.6.0", "axios": "^1.8.4", "cypress-cloud": "^1.13.1", "date-fns": "^2.30.0", "dompurify": "^3.2.4", "element-plus": "^2.11.0", "fuse.js": "^6.5.3", "lodash-es": "^4.17.21", "mitt": "^2.1.0", "pdfjs-dist": "^4.5.136", "pinia": "^2.2.4", "sass-embedded": "^1.86.3", "tailwindcss": "^3.4.4", "tippy.js": "^6.3.7", "unplugin-element-plus": "^0.9.1", "vue": "^3.5.13", "vue-router": "^4.4.5"}, "devDependencies": {"@pinia/testing": "^0.1.6", "@types/dompurify": "^2.4.0", "@types/estree": "^0.0.52", "@types/lodash-es": "^4.17.4", "@types/node": "^18.19.39", "@typescript-eslint/eslint-plugin": "^6.7.5", "@typescript-eslint/parser": "^6.7.5", "@vitejs/plugin-vue": "^5.1.4", "@vue/compiler-dom": "^3.4.38", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.21", "cssnano": "^5.0.15", "cypress": "^12.17.4", "eslint": "^8.51.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.25.4", "eslint-plugin-tailwindcss": "^3.18.2", "eslint-plugin-vue": "^9.28.0", "jsdom": "^21.1.0", "mini-svg-data-uri": "^1.4.4", "postcss": "^8.5.3", "rollup-plugin-analyzer": "^4.0.0", "ts-node": "^10.9.1", "typescript": "^4.9.5", "vite": "^5.4.19", "vitest": "^0.34.6", "vue-tsc": "^1.8.27"}}