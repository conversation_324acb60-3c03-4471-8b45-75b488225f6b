<template>
  <div
    class="html-preview"
    :class="{'hidden': !isHtmlContentExpanded}"
    v-html="sanitizedVerlauf"
  />
  <DsButton
    v-if="sanitizedVerlauf !== ''"
    :icon="isHtmlContentExpanded ? 'angle-up' : 'angle-right'"
    icon-align="right"
    variant="outline"
    size="sm"
    @click="isHtmlContentExpanded = !isHtmlContentExpanded"
  >
    Mailverlauf {{ isHtmlContentExpanded ? 'ausblenden' : 'einblenden' }}
  </DsButton>
</template>

<script setup lang="ts">
import { DsButton } from '@demvsystems/design-components';
import DOMPurify from 'dompurify';
import { computed, PropType, ref, watch } from 'vue';

import {
  useKorrespondenzenHistory,
} from '@/pages/korrespondenzDetails/components/useKorrespondenzenHistory';
import {
  BasicVorgangResource,
  ErinnerungResource,
  KorrespondenzResource,
  MahnungResource,
} from '@/store/resources/types';

const props = defineProps({
  vorgang: {
    type: Object as PropType<BasicVorgangResource>,
    default: null,
  },
  korrespondenz: {
    type: Object as PropType<KorrespondenzResource | MahnungResource | ErinnerungResource>,
    default: null,
  },
});
const isHtmlContentExpanded = ref(false);
const verlauf = ref('');

const {
  getHistoryForKorrespondenz,
} = useKorrespondenzenHistory();

watch(() => props.korrespondenz, async (
  newKorrespondenz: (KorrespondenzResource | MahnungResource | ErinnerungResource),
) => {
  if (props.vorgang === null || props.korrespondenz === null) {
    return;
  }

  verlauf.value = await getHistoryForKorrespondenz(props.vorgang, newKorrespondenz);
}, { immediate:true });

const sanitizedVerlauf = computed(() => (
  DOMPurify.sanitize(verlauf.value ?? '', {
    FORBID_TAGS: ['style'],
  })
));
</script>
