<template>
  <div class="flex items-center">
    <div class="w-24 shrink-0 text-sm font-medium text-gray-700">
      {{ title }}
    </div>

    <div class="-m-0.5">
      <DsTag
        v-for="empfaenger in empfaengers"
        :key="empfaenger.email"
        class="m-0.5"
      >
        {{ empfaenger.email }}
      </DsTag>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsTag } from '@demvsystems/design-components';

import { EmailElement } from '@/store/resources/types';

withDefaults(defineProps<{
  empfaengers: EmailElement[],
  title?: string,
}>(), {
  empfaengers: () => [],
  title: '',
});
</script>
