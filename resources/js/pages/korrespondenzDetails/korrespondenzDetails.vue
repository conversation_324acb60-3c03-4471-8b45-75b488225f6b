<template>
  <div class="flex flex-col">
    <DetailContentPreview
      :cc="korrespondenz.attributes.cc"
      :bcc="korrespondenz.attributes.bcc"
      :subject="korrespondenz.attributes.betreff"
      :empfaenger="emailEmpfaengerProxy"
      :versendet-at="korrespondenz.attributes.versendetAt"
      :suppress-errors-and-retry="suppressErrorsAndRetry"
      :versandart="props.korrespondenz.attributes.versandart === 'brief' ? Versandart.Brief : Versandart.Mail"
      :is-versendet="isVersendet"
      :content="sanitizedContent"
      :pdf-url="pdfViewUrl"
      :absender="sender"
    >
      <KorrespondenzVerlauf
        :korrespondenz="korrespondenz"
        :vorgang="vorgang"
      />
      <DsFormGroup
        v-if="files.length > 0"
        label="Anhänge"
        class="sm:w-1/2"
      >
        <FileList :files="files" />
      </DsFormGroup>
    </DetailContentPreview>
  </div>
</template>

<script setup lang="ts">
import { DsFormGroup } from '@demvsystems/design-components';
import DOMPurify from 'dompurify';
import { isArray } from 'lodash-es';
import { computed } from 'vue';

import DetailContentPreview from '@/components/DetailContentPreview.vue';
import FileList from '@/components/fileList/FileList.vue';
import KorrespondenzVerlauf from '@/pages/korrespondenzDetails/components/KorrespondenzVerlauf.vue';
import { Versandart } from '@/pages/vorlagen/types';
import {
  BaseFileResource,
  BasicVorgangResource,
  EmailElement,
  ErinnerungResource,
  KorrespondenzResource,
  MahnungResource,
} from '@/store/resources/types';
import { getPdfViewUrl } from '@/utils/pdfUrls';

const props = withDefaults(defineProps<{
  vorgang: BasicVorgangResource,
  korrespondenz: KorrespondenzResource | ErinnerungResource | MahnungResource,
  files: BaseFileResource[],
  expectedSender?: EmailElement | null,
  suppressErrorsAndRetry?: boolean,
}>(), {
  files: () => [],
  expectedSender: null,
});

const sender = computed(() => {
  if (props.expectedSender !== null) {
    return [props.expectedSender];
  }

  const absender = props.korrespondenz.attributes.absender;

  return (absender === undefined || absender === null) ? [] : [absender as EmailElement];
});

const isVersendet = computed(() => (
  props.korrespondenz.attributes.versendetAt != undefined
));

const emailEmpfaengerProxy = computed(() => {
  if (!isArray(props.korrespondenz.attributes.empfaenger)) {
    return [];
  }

  return props.korrespondenz.attributes.empfaenger as unknown as EmailElement[];
});

const sanitizedContent = computed(() => (
  DOMPurify.sanitize(props.korrespondenz.attributes.content ?? '', {
    FORBID_TAGS: ['style'],
  })
));

const pdfViewUrl = computed(() => {
  if (props.korrespondenz.attributes.versandart === 'brief') {
    return getPdfViewUrl(props.vorgang.id, props.korrespondenz.id);
  }

  return undefined;
});
</script>
