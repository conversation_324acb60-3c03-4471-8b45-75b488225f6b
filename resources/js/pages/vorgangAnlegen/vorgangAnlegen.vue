<template>
  <div class="flex flex-col">
    <header class="z-50 -mx-6 flex items-center space-x-3 px-6 pb-6 shadow">
      <DsButton
        v-if="step === 1"
        variant="secondary"
        icon="arrow-left"
        @click="cancelHandler"
      />
      <h2 class="grow text-lg font-bold leading-none tracking-wide">
        {{ vorgaenger !== undefined ? 'Folgevorgang' : 'Vorgang' }} anlegen
      </h2>

      <DsButton
        v-if="step === 0"
        variant="secondary"
        icon="xmark"
        @click="closeHandler"
      />
    </header>

    <div
      :class="{'bg-gray-100': step === 1 && vorgangAnlegenStore.versandart === 'brief'}"
      class="-mx-4 overflow-auto p-4 sm:-mx-6 sm:p-6"
    >
      <DsAlert
        v-if="
          vorgaenger !== undefined
            && (step === 0 || vorgangAnlegenStore.versandart === 'email')
        "
        class="mb-5"
        type="info"
        icon="fast-forward"
        :label="`Folgevorgang zu: ${vorgaenger?.attributes?.titel} (${vorgaenger?.attributes?.vorgangsnummer})`"
      />

      <DsAlert
        v-if="vorgangAnlegenStore.bezug !== null"
        class="mb-5"
        type="info"
        icon="circle-info"
        :label="`Sie erstellen einen Vorgang zu: „${vorgangAnlegenStore.bezug.benennung}”`"
      >
        Bitte beachten Sie, dass in Ihrem Vorgang eine Verknüpfung zu
        „{{ vorgangAnlegenStore.bezug?.benennung }}” hinterlegt wird.
        Falls Sie einen anderen Vorgang anlegen wollen,
        nutzen Sie bitte die Zurücksetzen-Funktion am unteren Ende dieses Fensters.
        <template
          v-if="vorgangAnlegenStore.lockedFields.size !== 0"
        >
          <br>
          Um diese Verknüpfung im richtigen Kontext zu behalten,
          wurden einige vorbefüllte Felder gesperrt.
        </template>
      </DsAlert>

      <component
        :is="stepComponent"
        v-if="stepComponent !== undefined"
      />
    </div>

    <footer class="-mx-6 flex items-center justify-end space-x-4 border-t px-6 pt-6">
      <DsButton
        v-if="step === 0"
        variant="danger"
        icon="eraser"
        size="lg"
        class="mr-auto"
        :disabled="isLoading"
        @click="resetHandler"
      >
        Zurücksetzen
      </DsButton>

      <DsFormGroup
        label="Weiteren Vorgang anlegen"
        label-for="createAnother"
        inline
      >
        <DsCheckbox
          id="createAnother"
          v-model="createAnother"
        />
      </DsFormGroup>

      <DsButton
        v-if="!inline || step > 0"
        :handler="cancelHandler"
        variant="secondary"
        size="lg"
      >
        {{ step > 0 ? 'Zurück' : 'Abbrechen' }}
      </DsButton>

      <DsButton
        :handler="okHandler"
        :disabled="vorgangAnlegenStore.canNotSubmit"
        size="lg"
      >
        {{ okLabel }}
      </DsButton>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { DsAlert, DsButton, DsCheckbox, DsFormGroup } from '@demvsystems/design-components';
import { isEmpty } from 'lodash-es';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { defineAsyncComponent } from '@/components/async';
import useCurrentUser from '@/components/users/useCurrentUser';
import { FirstTimelineElementType } from '@/pages/vorgangAnlegen/types';
import { Versandart } from '@/pages/vorlagen/types';
import { createStore } from '@/store/resources';
import { provideStore } from '@/store/resources/composition';
import { eventBus } from '@/store/resources/store';

import useVorgangAnlegenModal from './composables/useVorgangAnlegenModal';
import { useVorgangAnlegenStore } from './stores/vorgangAnlegenStore/vorgangAnlegenStore';

const vorgangAnlegenStore = useVorgangAnlegenStore();

defineProps<{
  inline?: boolean;
}>();

const emit = defineEmits<{
  (event:'close'): void
}>();

const store = createStore();
provideStore(store);

const router = useRouter();
const step = ref(0);
const createAnother = ref(false);
const isLoading = ref(false);

const okLabelConfig = computed<Record<FirstTimelineElementType, string[]>>(() => {
  return {
    korrespondenzen: [
      'Zur Vorschau',
      `Vorgang anlegen ${vorgangAnlegenStore.versandart === Versandart.Brief ? '& Brief generieren' : '& E-Mail versenden'}`,
    ],
    kommentare: [
      'Vorgang anlegen',
    ],
  };
});

const vorgangsComponentConfig = <Record<FirstTimelineElementType, unknown[]>>{
  korrespondenzen: [
    defineAsyncComponent(() => import('./components/korrespondenzen/step1.vue')),
    defineAsyncComponent(() => import('./components/korrespondenzen/step2.vue')),
  ],
  kommentare: [
    defineAsyncComponent(() => import('./components/aufgaben/step1.vue')),
  ],
};

const {
  vorgaenger,
} = useVorgangAnlegenModal();

onMounted(() => {
  if (vorgaenger.value !== undefined) {
    void vorgangAnlegenStore.initFromVorgaenger(vorgaenger.value);
  } else if (!isEmpty(router.currentRoute.value.query)) {
    void vorgangAnlegenStore.initFromQueryParams();
  }
});

const { user } = useCurrentUser();

const publish = async (): Promise<void> => {
  let vorgangsnummer = '';
  try {
    vorgangsnummer = await vorgangAnlegenStore.submit();
  } catch (e) {
    return;
  }

  eventBus.emit('vorgangErstellt');

  if (createAnother.value) {
    step.value = 0;

    return;
  }

  emit('close');

  await router.push({
    name: 'vorgaenge.show',
    params: {
      firmaId: user.value?.attributes.firmaId,
      vorgangsnummer: vorgangsnummer,
    },
    query: {
      isVorgangNew: 1,
    },
  });
};

const okLabel = computed<string>(() => (
  okLabelConfig.value[vorgangAnlegenStore.firstTimelineElementType][step.value] as string
));

const stepComponent = computed(() => (
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  vorgangsComponentConfig[vorgangAnlegenStore.firstTimelineElementType]?.[step.value]
));

const okHandler = async () => {
  const steps = vorgangsComponentConfig[vorgangAnlegenStore.firstTimelineElementType]?.length;

  if (step.value >= steps - 1) {
    await publish();

    return;
  }

  await vorgangAnlegenStore.submitAsEntwurf();

  step.value += 1;
};

const closeHandler = () => {
  if (vorgaenger.value !== undefined) {
    // empty store as it has been filled as a folgevorgang
    vorgangAnlegenStore.reset();
  }

  emit('close');
};

const cancelHandler = () => {
  if (step.value === 0) {
    closeHandler();

    return;
  }

  step.value = Math.max(0, step.value - 1);
};

const resetHandler = async () => {
  if (vorgaenger.value !== undefined && !isLoading.value) {
    isLoading.value = true;
    await vorgangAnlegenStore.initFromVorgaenger(vorgaenger.value);
    isLoading.value = false;

    return;
  }

  vorgangAnlegenStore.reset();
};
</script>
