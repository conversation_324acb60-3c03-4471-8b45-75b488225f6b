import { Ref, ref, UnwrapRef } from 'vue';

import { VorgangResource } from '@/store/resources/types';

const show = ref(false);
// for non-native-germans, this means the forerunner of the vorgang.
const vorgaenger = ref<VorgangResource>();

export default (): {
  show: Ref<UnwrapRef<boolean>>;
  close: () => void;
  open: (vorgaengerVorgang?: VorgangResource) => void;
  vorgaenger: Ref<VorgangResource | undefined>;
} => {
  return {
    show,
    vorgaenger,

    open: (vorgaengerVorgang?: VorgangResource) => {
      if (show.value === true) {
        return;
      }

      show.value = true;
      vorgaenger.value = vorgaengerVorgang;
    },

    close: () => {
      vorgaenger.value = undefined;
      show.value = false;
    },
  };
};
