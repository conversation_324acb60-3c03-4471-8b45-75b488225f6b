<template>
  <HeaderAction
    :handler="() => open()"
    label="Vorgang anlegen"
  />
  <DsModal
    :show="show"
    size="lg"
    anchor="top"
    hide-buttons
    custom-content
  >
    <VorgangAnlegen
      :vorgaenger="vorgaenger"
      data-test="vorgang-anlegen__modal"
      style="max-height: calc(100vh - 7rem)"
      @close="close"
    />
  </DsModal>
</template>

<script setup lang="ts">
import { DsModal } from '@demvsystems/design-components';
import { defineAsyncComponent, watch } from 'vue';
import { useRoute } from 'vue-router';

import HeaderAction from '@/components/header/HeaderAction.vue';

import useVorgangAnlegenModal from '../composables/useVorgangAnlegenModal';

const VorgangAnlegen = defineAsyncComponent(
  () => import('../vorgangAnlegen.vue'),
);

const {
  show,
  vorgaenger,
  open,
  close,
} = useVorgangAnlegenModal();

const route = useRoute();

watch(route, (value) => {
  const hasParams = Object.keys(value.query ?? {}).some((a) => a.includes('vorgangAnlegen['));

  if (hasParams) {
    open();
  }
});
</script>
