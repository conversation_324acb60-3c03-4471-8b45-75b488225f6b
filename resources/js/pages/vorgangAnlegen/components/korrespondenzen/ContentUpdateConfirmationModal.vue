<template>
  <DsModal
    title="Änderungen übernehmen?"
    :show="show"
    :variant="ModalVariant.Error"
    icon="input-text"
    size="sm"
    confirm-label="Ja, Vorlage laden"
    cancel-label="Nein, Änderungen behalten"
    action-required
    @confirm="updateContentAndClose"
    @cancel="close"
  >
    Sie haben Änderungen an Titel, Betreff und/oder Inhalt durchgeführt
    und sind im Begriff eine Vorlage zu laden.
    <b>Ihre Änderungen gehen damit unwiderruflich verloren.</b>
  </DsModal>
</template>

<script setup lang="ts">
import { DsModal, ModalVariant } from '@demvsystems/design-components';

import {
  useContentUpdateConfirmationModal,
} from '@/pages/vorgangAnlegen/composables/useContentUpdateConfirmationModal';
import {
  useVorgangAnlegenStore,
} from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/vorgangAnlegenStore';

const {
  show,
  close,
} = useContentUpdateConfirmationModal();

const vorgangAnlegenStore = useVorgangAnlegenStore();

function updateContentAndClose() {
  vorgangAnlegenStore.betreff = vorgangAnlegenStore.vorlageBetreff ?? null;
  vorgangAnlegenStore.content = vorgangAnlegenStore.vorlageContent ?? '';

  vorgangAnlegenStore.setTitelBySource();

  vorgangAnlegenStore.contentOrBetreffWasEdited = false;
  close();
}
</script>
