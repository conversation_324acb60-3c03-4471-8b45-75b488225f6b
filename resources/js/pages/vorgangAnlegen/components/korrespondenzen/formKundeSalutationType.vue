<template>
  <DsFormGroup label="Anrede">
    <DsSelect
      v-model="vorgangAnlegenStore.briefEmpfaenger.salutationType"
      :data="salutations"
      :disabled="!isEmpfaengerTypeKundeOrEigene"
      data-test="form-kunde-salutation-type__select"
    >
      <template #entry="{entry}">
        {{ entry.label }}
      </template>
    </DsSelect>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsSelect } from '@demvsystems/design-components';
import { computed } from 'vue';

import { SalutationType } from '../../../../store/resources/types';
import { useVorgangAnlegenStore } from '../../stores/vorgangAnlegenStore/vorgangAnlegenStore';
import { BriefEmpfaenger } from '../../types';

const vorgangAnlegenStore = useVorgangAnlegenStore();

const isEmpfaengerTypeKundeOrEigene = computed(() => [
  BriefEmpfaenger.Kunde,
  BriefEmpfaenger.Eigene,
].includes(vorgangAnlegenStore.briefEmpfaengerType));

const salutations = [
  {
    label: SalutationType.Herr,
    value: SalutationType.Herr,
  },
  {
    label: SalutationType.Frau,
    value: SalutationType.Frau,
  },
  {
    label: SalutationType.Firma,
    value: SalutationType.Firma,
  },
  {
    label: '---',
    value: SalutationType.Empty,
  },
];
</script>
