<template>
  <DsRadioGroup
    v-model="vorgangAnlegenStore.firstTimelineElementType"
    variant="card"
    class="mb-7 sm:grid-cols-2"
  >
    <DsRadioButton
      value="korrespondenzen"
      icon="comments-alt"
      label="Korrespondenz"
      data-test="index__radio-button__korrespondenzen"
    >
      E-Mail oder Brief an Kunde oder Gesellschaft
    </DsRadioButton>
    <DsRadioButton
      value="kommentare"
      icon="tasks"
      label="Aufgabe"
      data-test="index__radio-button__kommentare"
    >
      Interner Vorgang zur Verwaltung
    </DsRadioButton>
  </DsRadioGroup>
</template>

<script setup lang="ts">
import { DsRadioButton, DsRadioGroup } from '@demvsystems/design-components';

import { useVorgangAnlegenStore } from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/vorgangAnlegenStore';

const vorgangAnlegenStore = useVorgangAnlegenStore();
</script>
