import { createState, createStore as createStoreInternal } from './store';
import {
  KommentarResource,
  GespraechsnotizResource,
  BriefVorlageResource,
  ExterneKorrespondenzResource,
  FaelligkeitAenderungResource,
  FileResource,
  GesellschaftResource,
  KorrespondenzResource,
  MahnungResource,
  ErinnerungResource,
  KundeResource,
  MailVorlageResource,
  SparteResource,
  StatusAenderungResource,
  TimelineEintragResource,
  UserAenderungResource,
  VertragAenderungResource,
  UserResource,
  VerknuepfungResource,
  VertragResource,
  VorgangResource,
  VorlageResource,
  FolgeVorgangElementeResource,
  BezugResource,
  LoeschKommentarResource,
  ParticipantResource,
  NotificationResource,
  SystemkommentarResource,
} from './types';

// Do not define a return value here, so we can easily add new states
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const createStore = () => createStoreInternal({
  faelligkeitAenderungen: createState<FaelligkeitAenderungResource>('faelligkeitAenderungen'),
  userAenderung: createState<UserAenderungResource>('userAenderung'),
  vertragAenderung: createState<VertragAenderungResource>('vertragAenderungen'),
  gesellschaften: createState<GesellschaftResource>('gesellschaften'),
  korrespondenzen: createState<KorrespondenzResource>('korrespondenzen'),
  mahnungen: createState<MahnungResource>('mahnungen'),
  erinnerungen: createState<ErinnerungResource>('erinnerung'),
  kommentare: createState<KommentarResource>('kommentare'),
  systemkommentare: createState<SystemkommentarResource>('systemkommentare'),
  loeschKommentare: createState<LoeschKommentarResource>('loeschKommentare'),
  gespraechsnotizen: createState<GespraechsnotizResource>('gespraechsnotizen'),
  kunden: createState<KundeResource>('kunden'),
  sparten: createState<SparteResource>('sparten'),
  statusAenderungen: createState<StatusAenderungResource>('statusAenderungen'),
  externeKorrespondenzen: createState<ExterneKorrespondenzResource>('externeKorrespondenzen'),
  timelineEintraege: createState<TimelineEintragResource>('timelineEintraege'),
  users: createState<UserResource>('users'),
  verknuepfungen: createState<VerknuepfungResource>('verknuepfungen'),
  vertraege: createState<VertragResource>('vertraege'),
  vorgaenge: createState<VorgangResource>('vorgaenge'),
  participants: createState<ParticipantResource>('participants'),
  files: createState<FileResource>('files'),
  vorlagen: createState<VorlageResource>('vorlagen'),
  vorlagenBrief: createState<BriefVorlageResource>('vorlagenBrief'),
  vorlagenMail: createState<MailVorlageResource>('vorlagenMail'),
  folgeVorgangElemente: createState<FolgeVorgangElementeResource>('folgeVorgangElemente'),
  bezuege: createState<BezugResource>('bezuege'),
  notifications: createState<NotificationResource>('notifications'),
});
