import {
  computed, inject, InjectionKey, provide, Ref,
} from 'vue';
import { useRoute } from 'vue-router';

import { VorgangResource } from './types';

import { createStore } from '.';

type Store = ReturnType<typeof createStore>;

const StoreSymbol: InjectionKey<Store> = Symbol('Store');

export const provideStore = (store: Store): void => provide(StoreSymbol, store);

export const injectStore = (): Store => {
  const instance = inject(StoreSymbol, null);

  if (instance === null || instance === undefined) {
    throw new Error('Cannot inject store!');
  }

  return instance;
};

export const useVorgangFromRoute = (): Ref<VorgangResource | undefined> => {
  const route = useRoute();
  const store = injectStore();

  return computed(() => store.vorgaenge.getAll().find(
    (vorgang) => (
      vorgang.attributes.vorgangsnummer === route.params.vorgangsnummer
    )),
  );
};
