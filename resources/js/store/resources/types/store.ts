import { UnwrapRef } from 'vue';

import * as JSONAPI from '../../../types/jsonapi';

import { EventBus } from './events';

export interface ResourceState<
  R extends JSONAPI.ResourceObject = JSONAPI.ResourceObject,
> {
  events: {
    insert: EventBus<R>;
    update: EventBus<R>;
  };

  clear(): void;

  insert(resource: UnwrapRef<R>): void;

  update(resource: UnwrapRef<R>): void;

  find(id?: string): UnwrapRef<R> | undefined;

  findRelated<T extends JSONAPI.RelationshipObject<
    JSONAPI.ResourceLinkHasOne<JSONAPI.ExtractType<R>>
    >>(relationship?: T): UnwrapRef<R> | undefined;

  findAll(ids?: string[]): UnwrapRef<R>[];

  findAllRelated<T extends JSONAPI.RelationshipObject<
    JSONAPI.ResourceLinkHasMany<JSONAPI.ExtractType<R>>
    >>(relationship?: T): UnwrapRef<R>[];

  getAll(): UnwrapRef<R>[];
}

export interface Store {
  clear(): void;
  load(document: JSONAPI.Document): void;
  update(document: JSONAPI.Document): void;
}
