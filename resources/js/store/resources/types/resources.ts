import { HTMLContent } from '@tiptap/core';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  ErweiterteKundensuchePayload,
  ImNamenVon,
  KampagneMessageAdresse,
  KampagneMessageStatus,
  KampagneStatus,
  Versandart,
} from '@/pages/kampagnen/types';
import { BriefAbsender, BriefEmpfaenger, EmailType } from '@/pages/vorgangAnlegen/types';
import { VorgangStatus } from '@/types';
import * as JSONAPI from '@/types/jsonapi';
import { ResourceObject } from '@/types/jsonapi';

type HasTimestamps = {
  createdAt: string;
  updatedAt: string;
};

type HasAenderungen<T> = {
  neuerWert: T;
  vorherigerWert: T;
};

export enum VorlageMailEmpfaengerType {
  Gesellschaft = 'gesellschaft',
  Kunde = 'kunde',
  DEMV = 'demv',
  Versender = 'versender',
  ZustaendigerVermittler = 'zustaendiger_vermittler',
}

type Email = string;

export enum EmailStatus {
  Versandbereit = 'versandbereit',
  InVersand = 'in_versand',
  Abgeschlossen = 'abgeschlossen',
  Fehler = 'fehler',
}

export type VorlageMailEmpfaenger = VorlageMailEmpfaengerType | Email;

export type BezugElement = {
  benennung: string,
  url?: string,
  referenz?: string,
};

export type EmailElement = {
  email: string,
  name?: string,
};

export const isEmailElement = (x: Record<string, unknown>): x is EmailElement => 'email' in x && x.email !== '';

export const toEmailElement = (
  record: Record<string, unknown> | undefined,
): EmailElement | undefined => {
  if (record === undefined || !isEmailElement(record)) {
    return undefined;
  }

  return record;
};

export enum Vorgangsart {
  KorrespondenzEmail = 'korrespondenz_email',
  KorrespondenzBrief = 'korrespondenz_brief',
  Aufgabe = 'aufgabe',
  Dunkelverarbeitet = 'dunkelverarbeitet',
  Vorgangsgruppe = 'vorgangsgruppe',
}

export type BasicVorgangResource = JSONAPI.ResourceObject<'vorgaenge', {
  titel: string;
  faelligAt: string | null;
  status: VorgangStatus;
  vorgangsart: Vorgangsart;
  isWichtig: boolean;
  isGroup: boolean;
  isUntervorgang: boolean;
  vorgangsnummer: string;
  hasNotification?: boolean,
  faelligAtSortable?: string,
} & HasTimestamps, {
  vertraege: JSONAPI.RelationshipHasMany<VertragResource>,
  owner: JSONAPI.RelationshipHasOne<UserResource>,
  ersteller?: JSONAPI.RelationshipHasOne<UserResource>,
  kunde: JSONAPI.RelationshipHasOne<KundeResource>,
  gesellschaft: JSONAPI.RelationshipHasOne<GesellschaftResource>,
  vertriebsweg: JSONAPI.RelationshipHasOne<GesellschaftResource>,
  participants: JSONAPI.RelationshipHasMany<ParticipantResource>,
  sparte: JSONAPI.RelationshipHasOne<SparteResource>,
  timeline: JSONAPI.RelationshipHasMany<TimelineEintragResource>,
  vorgangTyp: JSONAPI.RelationshipHasOne<VorgangTypResource>,
  notifications: JSONAPI.RelationshipHasMany<NotificationResource>,
  // FIXME this is VorgangResource, but right now results in a recursive error
  verknuepfungen: JSONAPI.RelationshipHasMany<JSONAPI.ResourceObject>,
  folgeVorgaenge: JSONAPI.RelationshipHasMany<JSONAPI.ResourceObject>,
  vorgaenger: JSONAPI.RelationshipHasOne<JSONAPI.ResourceObject>,
  bezug: JSONAPI.RelationshipHasOne<BezugResource>,
}>;

export type GroupVorgang = JSONAPI.ResourceObject<'vorgaenge', {
  titel: string;
  faelligAt: string | null;
  status: VorgangStatus;
  isWichtig: boolean;
  isGroup: true;
  isUntervorgang: false;
  untervorgaengeCount: number;
  untervorgaengeErledigtCount: number;
  vorgangsnummer: string;
} & HasTimestamps, {
  unterVorgaenge: JSONAPI.RelationshipHasMany<BasicVorgangResource>,
  ueberVorgang: JSONAPI.RelationshipHasOne<BasicVorgangResource>,
  bezug: JSONAPI.RelationshipHasOne<BezugResource>,
}>;

export type VorgangResource = BasicVorgangResource & GroupVorgang;

export type FolgeVorgangElementeResource = ResourceObject<'folgeVorgangElemente', {
  vorgaenger_id: string;
  nachfolger_id: string;
}>;

export type AnsprechpartnerResource = ResourceObject<'ansprechpartner', {
  sex_descriptor: string;
  firstname: string;
  lastname: string;
  email: string;
  phone_business: string;
  phone_mobile: string;
  fax: string;
  address: string;
  homepage: string;
  department: string;
}>;

export type VorgangTypResource = JSONAPI.ResourceObject<'vorgangTypen', {
  titel: string;
  empfaengerTyp: string;
  isEmail: boolean;
  isBrief: boolean;
} & HasTimestamps>;

export enum NotificationSeverity {
  Error = 'error',
  Info = 'info',
}

export function sortNotificationSeverityByPriority(
  a: NotificationSeverity,
  b: NotificationSeverity,
): number {
  return Object.values(NotificationSeverity).indexOf(a)
    - Object.values(NotificationSeverity).indexOf(b);
}

export type NotificationResource = JSONAPI.ResourceObject<'notifications', {
  type: string;
  severity: NotificationSeverity;
} & HasTimestamps>;

export type BezugResource = JSONAPI.ResourceObject<'bezuege', {
  benennung: string;
  url: string;
  referenz: string;
} & HasTimestamps>;

export type VertragResource = JSONAPI.ResourceObject<'vertraege', {
  vertragsnummer: string;
  isSchaden: boolean;
  status: string;
  sparteId: number;
  gesellschaftId: number;
  kfzKennzeichen: string;
  bafinGesellschaftId: number;
  verwahrstelle: string;
}, {
  sparte: JSONAPI.RelationshipHasOne<SparteResource>,
  gesellschaft: JSONAPI.RelationshipHasOne<GesellschaftResource>,
  bafinGesellschaft: JSONAPI.RelationshipHasOne<GesellschaftResource>,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  kunden: JSONAPI.RelationshipHasMany<any>, // todo: fix recursion issue
}>;

export enum SalutationType {
  Herr = 'Herr',
  Frau = 'Frau',
  Firma = 'Firma',
  Empty = '',
}

export type KundeResource = JSONAPI.ResourceObject<'kunden', {
  externalId: string,
  name: string;
  firstName: string;
  lastName: string;
  title: string;
  email: string | null;
  informal: boolean;
  salutationType: SalutationType;
  userExternalId: string,
} & HasTimestamps, {
  vertraege: JSONAPI.RelationshipHasMany<VertragResource>,
}>;

export type GesellschaftResource = JSONAPI.ResourceObject<'gesellschaften', {
  name: string;
  abkuerzung: string | null;
  isPool: boolean;
  externalId: string;
} & HasTimestamps>;

export type FirmaRessource = JSONAPI.ResourceObject<'firmen', {
  name?: string;
  kuerzel?: string;
} & HasTimestamps>;

type BasicUser = {
  name: string;
  firstName: string;
  lastName: string;
  externalId: string;
  firmaId: number;
  isDeleted: boolean;
  status: string;
} & HasTimestamps;

export type UserResource = JSONAPI.ResourceObject<'users', BasicUser>;

export type AuthUserResource = JSONAPI.ResourceObject<'authUsers', {
  canImpersonate: boolean;
  isImpersonated: boolean;
  canCreateStandardVorlagen: boolean;
  excludeFromAnalytics: boolean;
  intercomHash: string;
  canFirmendatenVerwalten: boolean;
  canUseTextGeneration: boolean;
} & BasicUser, {
  firma: JSONAPI.RelationshipHasOne<FirmaRessource>,
}>;

export type BriefAddressElement = {
  salutationType?: SalutationType;
  titel?: string;
  name: string;
  fax?: string;
  adresszeile1: string;
  adresszeile2?: string;
  plz: string;
  stadt: string;
  land: string;
};

export enum KorrespondenzStatus {
  InBearbeitung = 'in_bearbeitung',
  Abgeschlossen = 'abgeschlossen',
  Fehler = 'fehler',
  Entwurf = 'entwurf',
}

type BasicKorrespondenz = {
  versandart: 'brief' | 'email';
  briefDatum?: string;
  betreff: string;
  content?: string;
  absender?: EmailElement | BriefAddressElement | null;
  empfaenger: EmailElement[] | BriefAddressElement;
  cc: EmailElement[];
  bcc: EmailElement[];
  hasCcAndBcc: boolean;
  versendetAt: string;
  emailType?: EmailType;
  briefAbsenderType?: BriefAbsender;
  status: KorrespondenzStatus;
  displayErrorBadge?: boolean; // todo: remove after new mailer started sending user mails
  errorMessage?: string;
};

export type KorrespondenzResource = JSONAPI.ResourceObject<'korrespondenzen', BasicKorrespondenz, {
  files: JSONAPI.RelationshipHasMany<FileResource>
}>;

export type MahnungResource = JSONAPI.ResourceObject<'mahnungen', BasicKorrespondenz, {
  files: JSONAPI.RelationshipHasMany<FileResource>
}>;

export type ErinnerungResource = JSONAPI.ResourceObject<'erinnerungen', BasicKorrespondenz, {
  files: JSONAPI.RelationshipHasMany<FileResource>
}>;

type BasicKommentar = {
  content: string;
};

export type KommentarResource = JSONAPI.ResourceObject<'kommentare', BasicKommentar, {
  files: JSONAPI.RelationshipHasMany<FileResource>
}>;

export type SystemkommentarResource = JSONAPI.ResourceObject<'systemkommentare', BasicKommentar, {
  files: JSONAPI.RelationshipHasMany<FileResource>
}>;

export type LoeschKommentarResource = JSONAPI.ResourceObject<'loeschKommentare', {
  content: string;
  createdAt: string;
  deletedBy: string;
  elementType: string;
}>;

export enum GespraechsType {
  Phone = 'phone',
  OnSite = 'on-site',
  Other = 'other',
}

export type GespraechsnotizResource = JSONAPI.ResourceObject<'gespraechsnotizen',
  BasicKommentar & {
    type: GespraechsType,
    dateFrom: string,
    dateTo: string,
  }, {
    files: JSONAPI.RelationshipHasMany<FileResource>
  }
>;

export type SparteResource = JSONAPI.ResourceObject<'sparten', {
  name: string;
  displayName: string;
  abkuerzung: string;
  externalId: string;
}>;

export type VerknuepfungResource = JSONAPI.ResourceObject<'verknuepfungen', {
  aktion: 'erstellt' | 'entfernt';
}, {
  vorgang: JSONAPI.RelationshipHasOne<VorgangResource>,
}>;

export type StatusAenderungResource = JSONAPI.ResourceObject<
  'statusAenderungen',
  HasAenderungen<VorgangStatus>
>;

export type ExterneKorrespondenzResource = JSONAPI.ResourceObject<
  'externeKorrespondenzen', {
    usesAttachments: boolean;
  }, {
    files: JSONAPI.RelationshipHasMany<FileResource>,
  }>;

export type FaelligkeitAenderungResource = JSONAPI.ResourceObject<
  'faelligkeitAenderungen',
  HasAenderungen<VorgangStatus>
>;

export type UserAenderungResource = JSONAPI.ResourceObject<'userAenderungen', {
  action: string,
  author: string,
  assignedUser: string,
  task: string,
}>;

export type VertragAenderungResource = JSONAPI.ResourceObject<'vertragAenderungen', {
  action: string,
}>;

export type TimelineEintragResource = JSONAPI.ResourceObject<'timelineEintraege', {
  isDeletable: boolean,
} & HasTimestamps, {
  ersteller: JSONAPI.RelationshipHasOne<UserResource>,
  owner: JSONAPI.RelationshipHasOne<UserResource>,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  element: JSONAPI.RelationshipHasOne<any> // todo: fix recursion issue
}>;

export type KundenDocumentResource = JSONAPI.ResourceObject<'kundenDocuments', {
  name: string,
  extension: string,
  url: string,
  size?: number,
  vertragId: string | null,
  gesellschaftName: string | null,
  gesellschaftId: string | null,
  sparteName: string | null,
  dokumentTypName: string | null,
} & HasTimestamps>;

export type DokumenttypRessource = JSONAPI.ResourceObject<'dokumenttypen', {
  id: number,
  name: string,
}>;

export type BaseFileResource = JSONAPI.ResourceObject<'files', {
  name: string;
  size?: number;
}, {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  owner: JSONAPI.RelationshipHasOne<any>
}>;

// existing files from backend
export type FileResource = BaseFileResource & JSONAPI.ResourceObject<'files', {
  mimetype: string;
  size: number;
  status: 'pending' | 'uploaded' | 'error';
  hidden?: boolean;
}>;

// files the user just uploaded in vorgaenge
export type UploadedFileResource = FileResource & JSONAPI.ResourceObject<'files', {
  tempPath: string;
}>;

export type ExternalFileOrigin = 'kundenakte' | 'aktuellerVorgang';

// files from an external source, e.g. Pw
export type ExternalFileResource = BaseFileResource & JSONAPI.ResourceObject<'files', {
  url: string;
  origin: ExternalFileOrigin; // only needed in frontend
}>;

export type AutoAttachmentExternalFileResource =
  ExternalFileResource
  & JSONAPI.ResourceObject<'files', {
    auto: true; // only needed in frontend
  }>;

export const isFileResource = (
  x: BaseFileResource,
): x is FileResource => (
  'mimetype' in x.attributes && 'size' in x.attributes && 'status' in x.attributes
);

// fix-me: is not working as it should.
export const isUploadedFileResource = (
  x: BaseFileResource,
): x is UploadedFileResource => (
  isFileResource(x) && x.attributes.mimetype !== null
);

export const isExternalFileResource = (
  x: BaseFileResource,
): x is ExternalFileResource => (
  'url' in x.attributes
);

export type ExpectedSenderResource = JSONAPI.ResourceObject<'expectedSender', EmailElement>;

export type PwAddressResource = JSONAPI.ResourceObject<'address', {
  strasse: string,
  plz: string,
  ort: string,
  namensZusatz: string,
  fax?: string,
  hausnummer: string,
  adresszusatz: string | null,
  country: {
    id: number,
    iso: string,
    name: string,
    nationalityName: string,
  } | null,
  isHauptAdresse?: boolean,
}>;

// attachmentName -> dokumenttypName
export enum Attachment {
  Maklervertrag = 'Maklervertrag - Maklerauftrag',
  Maklervollmacht = 'Maklervollmacht',
  MaklerauftragBlanko = 'Personalisierter Maklerauftrag (Blanko)',
  ReisepassPersonalausweis = 'Reisepass / Personalausweis',
}

export const attachmentLabels: Record<Attachment, string> = {
  [Attachment.Maklervertrag]: 'Maklervollmacht - Maklervertrag',
  [Attachment.Maklervollmacht]: 'Maklervollmacht',
  [Attachment.MaklerauftragBlanko]: 'Personalisierter Maklerauftrag (Blanko)',
  [Attachment.ReisepassPersonalausweis]: 'Reisepass / Personalausweis',
};

export type VorlageResource = JSONAPI.ResourceObject<'vorlagen', {
  name: string,
  description?: string,
  type?: string,
  usageByOwnerOnly: boolean,
  isEditable?: boolean, // only from backend (only read)
  isBasisvorlage: boolean,
  basisvorlageVorgangstyp: string,
  basisvorlageVorlagetyp: string,
} & HasTimestamps, {
  ersteller: JSONAPI.RelationshipHasOne<UserResource>,
  // eslint-disable-next-line vue/max-len
  vorlage: JSONAPI.RelationshipHasOne<MailVorlageResource> | JSONAPI.RelationshipHasOne<BriefVorlageResource> | JSONAPI.RelationshipHasOne<KampagneMailVorlageResource> | JSONAPI.RelationshipHasOne<KampagneBriefVorlageResource>,
}>;

export type BriefVorlageResource = JSONAPI.ResourceObject<'vorlagenBrief', {
  empfaengerType?: BriefEmpfaenger,
  senderTyp?: BriefAbsender,
  formalContent: HTMLContent,
  informalContent?: HTMLContent,
}, {
  vorgangTyp: JSONAPI.RelationshipHasOne<VorgangTypResource>,
}>;

export type MailVorlageResource = JSONAPI.ResourceObject<'vorlagenMail', {
  empfaengerTypes: VorlageMailEmpfaenger[],
  cc: VorlageMailEmpfaenger[],
  bcc: VorlageMailEmpfaenger[],
  attachments: Attachment[],
  formalSubject: HTMLContent,
  formalContent: HTMLContent,
  informalSubject?: HTMLContent,
  informalContent?: HTMLContent,
}, {
  vorgangTyp: JSONAPI.RelationshipHasOne<VorgangTypResource>,
}>;

export type KampagneMailVorlageResource = JSONAPI.ResourceObject<'vorlagenKampagneMail', {
  attachments: Attachment[],
  formalSubject: HTMLContent,
  formalContent: HTMLContent,
  informalSubject?: HTMLContent,
  informalContent?: HTMLContent,
}>;
export type KampagneBriefVorlageResource = JSONAPI.ResourceObject<'vorlagenKampagneBrief', {
  formalContent: HTMLContent,
  informalContent?: HTMLContent,
}>;

export type FirstTimelineElementPayload = {
  content: string | undefined;
  absender?: EmailElement;
  versandart?: 'brief' | 'email';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
} & Record<string, any>;

export type FirstTimelineElementResource = ResourceObject<string, FirstTimelineElementPayload>;

export enum ParticipantType {
  Bearbeiter = 'bearbeiter',
  Beobachter = 'beobachter',
}

export type ParticipantResource = JSONAPI.ResourceObject<'participants', {
  participantType: ParticipantType,
  userId: string,
} & HasTimestamps>;

export type KampagneMessageResource = JSONAPI.ResourceObject<'kampagnenMessages', {
  kampagneId: string,
  kundeExternalId: string,
  senderExternalId: string,
  informal: boolean,
  name: string,
  email?: string | null,
  betreff?: string | null,
  content: string,
  status?: KampagneMessageStatus,
  adresse?: KampagneMessageAdresse | null,
} & HasTimestamps>;

export type KampagneResource = JSONAPI.ResourceObject<'kampagnen', {
  versandart: Versandart,
  titel: string | null,
  vorgaengeAnlegen: boolean,
  imNamenVon: ImNamenVon,
  senderExternalId?: string | null,
  empfaengers?: Empfaenger[] | null,
  formalBetreff: string | null,
  formalContent: string | null,
  informalBetreff: string | null,
  informalContent: string | null,
  attachments: Attachment[],
  status?: KampagneStatus,
  geplantAt?: string | null,
  versendetAt?: string | null,
  senderLabel?: string,
  messagesCount?: number,
  failedMessages?: number,
  sentMessages?: number,
  vorgangId?: string | null,
  kampagneIdeeId?: string | null,
} & Partial<HasTimestamps>, {
  files: JSONAPI.RelationshipHasMany<FileResource>,
  messages?: JSONAPI.RelationshipHasMany<KampagneMessageResource>,
  kampagneIdee: JSONAPI.RelationshipHasOne<KampagneIdeeResource>,
}>;

export type EmailMetaResource = JSONAPI.ResourceObject<'emailMetas', {
  versendetAt: string,
  empfaenger: EmailElement[],
  absender: EmailElement,
  cc?: EmailElement[] | null,
  bcc?: EmailElement[] | null,
  status?: EmailStatus,
  statusText?: string | null,
  ccActual?: EmailElement[] | null,
  bccActual?: EmailElement[] | null,
  empfaengerActual?: EmailElement[] | null,
  absenderActual?: EmailElement | null,
  messageId?: string | null
} & HasTimestamps>;

export type KampagneIdeeResource = JSONAPI.ResourceObject<'kampagneIdeen', {
  name: string,
  description: string,
  kampagneTitel: string,
  briefVorlageId: string,
  mailVorlageId: string,
  kundensucheVorlage: Partial<ErweiterteKundensuchePayload> | null,
  isHighlighted: boolean
}>;
