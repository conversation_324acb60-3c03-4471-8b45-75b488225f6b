import { EventType } from 'mitt';

import type { ResourceObject } from '@/types/jsonapi';

import type { ResourceState } from './store';

export type StateEvent<R extends ResourceObject> = {
  resource: R,
  state: ResourceState<R>,
};

export type StateEventHandler<R extends ResourceObject>
  = (event: StateEvent<R>) => void;

export interface EventBus<R extends ResourceObject> {
  type: EventType,

  /**
   * Subscribes to the current event bus.
   * Returns a callback to unsubscribe the handler again.
   *
   * @param handler
   */
  subscribe(handler: StateEventHandler<R>): () => void;
}
