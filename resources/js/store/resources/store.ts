import { tryOnUnmounted } from '@vueuse/core';
import { isArray, mergeWith } from 'lodash-es';
import mitt, { EventType, Handler } from 'mitt';
import { ref, UnwrapRef } from 'vue';

import {
  Document, ExtractType, ResourceIdentifier, ResourceObject,
} from '@/types/jsonapi';

import {
  EventBus, ResourceState, StateEvent, Store,
} from './types';

export const eventBus = mitt();

const createEventBus = <R extends ResourceObject>(
  type: EventType,
): EventBus<R> => ({
  type,

  subscribe(handler) {
    const callback: Handler<StateEvent<R>> = (event) => {
      if (event !== undefined) {
        handler(event);
      }
    };

    eventBus.on<StateEvent<R>>(type, callback);

    tryOnUnmounted(() => {
      eventBus.off(type, callback);
    });

    return () => {
      eventBus.off(type, callback);
    };
  },
});

export const createState = <R extends ResourceObject>(name: string): ResourceState<R> => {
  const ids = ref<string[]>([]);
  const entities = ref<Record<string, UnwrapRef<R>>>({});

  const events = {
    insert: createEventBus<R>(`${name}:insert`),
    update: createEventBus<R>(`${name}:update`),
  };

  const state: ResourceState<R> = {
    events,

    clear() {
      ids.value = [];
      entities.value = {};
    },

    insert(resource) {
      ids.value.push(resource.id);
      entities.value[resource.id] = resource;

      eventBus.emit(events.insert.type, { resource, state });
    },

    update(resource) {
      if (ids.value.includes(resource.id)) {
        entities.value[resource.id] = mergeWith(
          entities.value[resource.id],
          resource,
          // replace arrays becauce we don't want to merge them
          (oldAttribute, newAttribute) => (
            isArray(newAttribute) ? newAttribute : undefined
          ),
        );

        eventBus.emit(events.update.type, { resource, state });
      }
    },

    find(id = '') {
      return entities.value[id];
    },

    findRelated(relationship) {
      const id = relationship?.data?.id;

      return id !== undefined ? entities.value[id] : undefined;
    },

    findAll(modelIds = []) {
      return modelIds
        .map((id) => entities.value[id])
        .filter((record): record is UnwrapRef<R> => record !== undefined);
    },

    findAllRelated(relationship) {
      if (relationship === undefined) {
        return [];
      }

      return relationship.data
        .map((entry: ResourceIdentifier<ExtractType<R>>) => (
          entities.value[entry.id]
        ))
        .filter((record): record is UnwrapRef<R> => record !== undefined);
    },

    getAll() {
      return ids.value
        .map((id) => entities.value[id])
        .filter((record): record is UnwrapRef<R> => record !== undefined);
    },
  };

  return state;
};

export const createStore = <T extends Record<string, ResourceState>>(
  resources: T,
): Store & T => {
  const loadResource = (resource: ResourceObject, update: boolean, customType?: string) => {
    const state = resources[customType ?? resource.type];
    if (state === undefined) {
      return;
      // throw new Error(`Unsupported resource type: ${resource.type}`);
    }

    // Only update if resource exists
    if (update && state.find(resource.id) !== undefined) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      state.update(resource);

      return;
    }

    state.insert(resource);
  };

  const load = (document: Document, update: boolean) => {
    // Load Data
    if (document.data !== undefined) {
      if (Array.isArray(document.data)) {
        document.data.forEach((resource) => loadResource(resource, update));
      } else {
        loadResource(document.data, update);
      }
    }

    // Load Included
    if (document.included !== undefined) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access

      document.included.forEach((resource) => {
        loadResource(resource, update);
      });
    }
  };

  return {
    ...resources,

    load(document: Document) {
      load(document, false);
    },

    update(document: Document) {
      load(document, true);
    },

    clear() {
      Object.values(resources).forEach((state) => {
        state.clear();
      });
    },
  };
};
