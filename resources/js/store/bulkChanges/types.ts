import * as JSONAPI from '../../types/jsonapi';

export enum BulkChangeStatus {
  InBearbeitung = 'in_bearbeitung',
  Abgeschlossen = 'abgeschlossen',
  Fe<PERSON> = 'fehler',
}

const deletableResourceType = ['vorgaenge'];
const completableVorgangResourceType = ['vorgaenge'];
export type DeletableResourceType = typeof deletableResourceType[number];
export type CompletableVorgangResourceType = typeof completableVorgangResourceType[number];

export const isDeletableResourceType = (
  type: string,
): type is DeletableResourceType => (
  deletableResourceType.includes(type)
);

export const isCompletableVorgangRessourceType = (
  type: string,
): type is CompletableVorgangResourceType => (
  completableVorgangResourceType.includes(type)
);

type BulkChangeAttributes = {
  type: string;
  ids: string[];
  status: BulkChangeStatus;
};

export type BulkDeleteResource = JSONAPI.ResourceObject<'bulkDeletes', {
  type: DeletableResourceType;
} & BulkChangeAttributes>;

export type BulkCompleteVorgangResource = JSONAPI.ResourceObject<'bulkCompletesVorgang', {
  type: CompletableVorgangResourceType;
} & BulkChangeAttributes>;
