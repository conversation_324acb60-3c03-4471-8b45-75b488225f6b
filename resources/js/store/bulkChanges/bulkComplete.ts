import { get, post } from '@/api';

import { BulkChangeStatus, BulkCompleteVorgangResource, isCompletableVorgangRessourceType } from './types';

async function wait(ms: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}

export default async (type: string, ids: string[]):Promise<boolean> => {
  if (!isCompletableVorgangRessourceType(type)) {
    return false;
  }

  try {
    const { data: bulkCompleteVorgang } = await post<BulkCompleteVorgangResource>('bulkCompletesVorgang', {
      data: {
        type: 'bulkCompletesVorgang',
        attributes : {
          type,
          ids,
        },
      },
    });

    const id = bulkCompleteVorgang.data?.id;

    let status = bulkCompleteVorgang.data?.attributes.status;

    while (status === BulkChangeStatus.InBearbeitung) {
      await wait(500);

      const { data: polledBulkCompleteVorgang } = await get<BulkCompleteVorgangResource>(
        `/bulkCompletesVorgang/${id}`,
      );

      status = polledBulkCompleteVorgang.data?.attributes.status;
    }

    return status === BulkChangeStatus.Abgeschlossen;
  } catch (e) {
    return false;
  }
};
