import { get, post } from '@/api';

import {
  BulkChangeStatus,
  BulkDeleteResource,
  isDeletableResourceType,
} from './types';

async function wait(ms: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}

export default async (type: string, ids: string[]): Promise<boolean> => {
  if (!isDeletableResourceType(type)) {
    return false;
  }

  try {
    const { data: bulkDelete } = await post<BulkDeleteResource>('/bulkDeletes', {
      data: {
        type: 'bulkDeletes',
        attributes: {
          type,
          ids,
        },
      },
    });

    const id = bulkDelete.data?.id;

    let status = bulkDelete.data?.attributes.status;

    while (status === BulkChangeStatus.InBearbeitung) {
      await wait(500);

      const { data: polledBulkDelete } = await get<BulkDeleteResource>(
        `/bulkDeletes/${id}`,
      );

      status = polledBulkDelete.data?.attributes.status;
    }

    return status === BulkChangeStatus.Abgeschlossen;
  } catch (e) {
    return false;
  }
};
