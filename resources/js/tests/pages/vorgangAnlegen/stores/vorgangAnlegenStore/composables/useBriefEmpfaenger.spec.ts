import { createPinia, setActivePinia } from 'pinia';
import { beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';

import * as api from '@/api';
import {
  useBriefEmpfaenger,
} from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/composables/useBriefEmpfaenger';
import usersMe from '@/tests/data/responses/users/me.json';

describe('useBriefEmpfaenger', () => {
  beforeAll(() => {
    // necessary because useBriefEmpfaenger uses vorgangAnlegenStore
    setActivePinia(createPinia());
  });

  beforeEach(() => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    vi.spyOn(api, 'get').mockResolvedValue(usersMe);
  });

  it('initializes with an initial briefEmpfaenger object', () => {
    const {
      briefEmpfaenger,
    } = useBriefEmpfaenger();

    expect(briefEmpfaenger.value).toEqual({
      name: '',
      titel: '',
      fax: '',
      adresszeile1: '',
      adresszeile2: '',
      plz: '',
      stadt: '',
      land: 'Deutschland',
    });
  });

  it('resets brief empfaenger', () => {
    const {
      briefEmpfaenger,
      resetBriefEmpfaenger,
    } = useBriefEmpfaenger();
    briefEmpfaenger.value = {
      name: 'Hans',
      titel: '',
      fax: '1337',
      adresszeile1: 'Irgendwo',
      adresszeile2: 'Nirgendwo',
      plz: '12345',
      stadt: 'Entenhausen',
      land: 'Mittelerde',
    };

    resetBriefEmpfaenger();

    expect(briefEmpfaenger.value).toEqual({
      name: '',
      titel: '',
      fax: '',
      adresszeile1: '',
      adresszeile2: '',
      plz: '',
      stadt: '',
      land: 'Deutschland',
    });
  });
});
