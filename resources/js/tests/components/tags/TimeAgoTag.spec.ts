import { createTesting<PERSON><PERSON> } from '@pinia/testing';
import { mount } from '@vue/test-utils';
import { afterAll, beforeAll, describe, expect, it, vi } from 'vitest';

import TimeAgoTag from '@/components/tags/TimeAgoTag.vue';
import { DateFormat } from '@/pages/settings/types';

beforeAll(() => {
  vi.useFakeTimers();

  // 01.01.2021 (month starts at 0)
  vi.setSystemTime(new Date(2021, 0, 1));
});

afterAll(() => {
  vi.useRealTimers();
});

describe('TimeAgoTag', () => {
  const dateMapping = {
    // 01.01.2021 (month starts at 0)
    '2020-06-01': '01.06.2020',
    '2020-06-02': '2. Juni',

    // grenze 6 tage vor heute
    '2020-12-25': '25. Dezember',
    '2020-12-26': 'letzten Samstag',

    // vorgestern bis übermorgen
    '2020-12-30': 'letzten Mitt<PERSON>ch',
    '2020-12-31': 'gestern',
    '2021-01-01': 'heute',
    '2021-01-02': 'morgen',
    '2021-01-03': 'Sonntag',

    // grenze 6 tage nach heute
    '2021-01-07': 'Donnerstag',
    '2021-01-08': '8. Januar',

    // grenze 6 monate nach heute
    '2021-07-31': '31. Juli',
    '2021-08-01': '01.08.2021',
  };

  Object.entries(dateMapping).forEach(([inputDate, expectedDate]) => {
    it(`displays ${inputDate} as ${expectedDate} with dateFormat=Dynamisch`, () => {
      expectTagToDisplayDate(inputDate, expectedDate, DateFormat.Dynamisch);
    });
  });

  it('displays dates with dateFormat=Einheitlich', () => {
    expectTagToDisplayDate('2021-01-07', '07.01.2021', DateFormat.Einheitlich);
    expectTagToDisplayDate('2020-06-02', '02.06.2020', DateFormat.Einheitlich);
    expectTagToDisplayDate('2021-01-01', '01.01.2021', DateFormat.Einheitlich);
  });
});

function expectTagToDisplayDate(inputDate: string, expectedDate: string, format: DateFormat) {
  const testingPinia = createTestingPinia({
    initialState: {
      userSettings: {
        dateFormat: format,
      },
    },
  });

  const wrapper = mount(TimeAgoTag, {
    global: {
      plugins: [testingPinia],
    },
    props: {
      value: inputDate,
    },
  });

  expect(wrapper.text()).toBe(expectedDate);
}
