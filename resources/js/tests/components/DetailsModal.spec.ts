import { DsButton, DsModal } from '@demvsystems/design-components';
import { mount, VueWrapper } from '@vue/test-utils';
import { describe, expect, it } from 'vitest';
import { ComponentPublicInstance } from 'vue';

import {
  BasicVorgangResource,
  FileResource,
  KorrespondenzResource,
  KorrespondenzStatus,
} from '@/store/resources/types';

import DetailsModal from '../../domains/korrespondenzen/components/DetailsModal.vue';

describe('DetailsModal', () => {
  it('modal opens with a click', async () => {
    const wrapper = mount(DetailsModal, {
      props: {
        vorgang: {
          id: '187',
        } as BasicVorgangResource,
        korrespondenz: {
          type: 'korrespondenzen',
          id: '187',
          attributes: {
            vorgangTyp: '3',
            versandart: 'brief',
            betreff: 'Quas recusandae ab sapiente voluptas aut ut qui et. Nemo quis reiciendis impedit velit distinctio et deserunt. Est repudiandae vitae molestiae quo in voluptates est. Omnis repellendus quo aut repellendus impedit sit.',
            content: '<p>Guten Tag,</p><p>Mädchen zur Maus herein an der rechten Hand und küßte es mit Händen greifen. Ich gestehe dir gern, denn ich habe in einem singenden Silbenfall an einem Tische Milch aßen und.</p><p>Sie, daß ich immer morgen wiederkommen würde. Heute war ich hinausgegangen, Lottens Klavier zu stimmen, ich konnte mich nicht enthalten, ihn, ungeachtet seines kleinen.</p><p>Mit freundlichen Grüßen</p>',
            absender: { email: '<EMAIL>' },
            empfaenger: [],
            cc: [],
            bcc: [],
            hasCcAndBcc: false,
            versendetAt: '1982-12-27T05:17:27+00:00',
            status: KorrespondenzStatus.Entwurf,
          },
        } as KorrespondenzResource,
        files: [
          {
            type: 'files',
            id: '42',
            attributes: {
              name: 'image.jpg',
              mimetype: 'image/jpeg',
              size: 123456,
            },
          },
        ] as FileResource[],
      },
    });

    const modal = (
      wrapper.findComponent(DsModal) as VueWrapper<ComponentPublicInstance<{ show: boolean }>>
    );
    expect(modal.props().show).toBe(false);

    await wrapper.findComponent(DsButton).find('button').trigger('click');
    expect(modal.props().show).toBe(true);
  });

  it('modal closes with a click', async () => {
    const wrapper = mount(DetailsModal, {
      props: {
        vorgang: {
          attributes: {
            status: 'offen',
          },
        } as BasicVorgangResource,
        korrespondenz: {
          type: 'korrespondenzen',
          id: '187',
          attributes: {
            vorgangTyp: '3',
            versandart: 'brief',
            betreff: 'Quas recusandae ab sapiente voluptas aut ut qui et. Nemo quis reiciendis impedit velit distinctio et deserunt. Est repudiandae vitae molestiae quo in voluptates est. Omnis repellendus quo aut repellendus impedit sit.',
            content: '<p>Guten Tag,</p><p>Mädchen zur Maus herein an der rechten Hand und küßte es mit Händen greifen. Ich gestehe dir gern, denn ich habe in einem singenden Silbenfall an einem Tische Milch aßen und.</p><p>Sie, daß ich immer morgen wiederkommen würde. Heute war ich hinausgegangen, Lottens Klavier zu stimmen, ich konnte mich nicht enthalten, ihn, ungeachtet seines kleinen.</p><p>Mit freundlichen Grüßen</p>',
            absender: { email: '<EMAIL>' },
            empfaenger: [],
            cc: [],
            bcc: [],
            hasCcAndBcc: false,
            versendetAt: '1982-12-27T05:17:27+00:00',
            status: KorrespondenzStatus.Entwurf,
          },
        } as KorrespondenzResource,
        files: [
          {
            type: 'files',
            id: '42',
            attributes: {
              name: 'image.jpg',
              mimetype: 'image/jpeg',
              size: 123456,
              status: 'pending',
            },
          },
        ],
      },
    });

    const modal = (
      wrapper.findComponent(DsModal) as VueWrapper<ComponentPublicInstance<{ show: boolean }>>
    );

    await wrapper.findComponent(DsButton).find('button').trigger('click');
    expect(modal.props().show).toBe(true);

    await modal.findAllComponents(DsButton)
      .find((button) => button.text() === 'Schließen')
      ?.find('button')
      .trigger('click');
    expect(modal.props().show).toBe(false);
  });
});
