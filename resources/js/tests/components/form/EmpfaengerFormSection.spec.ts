import { mount } from '@vue/test-utils';
import { describe, expect, it } from 'vitest';

import EmpfaengerFormSection from '@/components/form/EmpfaengerFormSection.vue';

describe('EmpfaengerToggleButton', () => {
  it('shows bcc and cc if prop is true', () => {
    const wrapper = mount(EmpfaengerFormSection, {
      props: {
        hasCcAndBcc: true,
      },
    });

    expect(wrapper.find('[data-test="empfaenger__cc"]').isVisible()).toBe(true);
    expect(wrapper.find('[data-test="empfaenger__bcc"]').isVisible()).toBe(true);
  });

  it('emits an event with hasCcAndBcc', async () => {
    const wrapper = mount(EmpfaengerFormSection, {
      props: {
        hasCcAndBcc: true,
      },
    });
    const dsSwitch = wrapper.find('[data-test="empfaenger__cc-bcc__switch"] button');
    await dsSwitch.trigger('click');

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.emitted('update:hasCcAndBcc')[0][0]).toBe(false);
  });
});
