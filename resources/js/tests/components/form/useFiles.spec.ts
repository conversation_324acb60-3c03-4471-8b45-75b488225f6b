import { describe, expect, it, vi } from 'vitest';

import * as api from '@/api';
import { useFiles } from '@/components/form/useFiles';
import {
  ExternalFileResource,
  UploadedFileResource,
} from '@/store/resources/types';

describe('useFiles', () => {
  it('separates files into external and uploaded', () => {
    const uploadedFile = <UploadedFileResource>{
      id: '',
      type: 'files',
      attributes: {
        name: 'uploadedFile.pdf',
        mimetype: 'application/pdf',
        size: 1337,
        tempPath: 'tmp/123-456-789',
        status: 'pending',
      },
    };
    const externalFile = <ExternalFileResource>{
      id: '',
      type: 'files',
      attributes: {
        name: 'externalFile.jpg',
        url: 'http://vorgaenge.demv.internal/some/file/path',
        origin: 'aktuellerVorgang',
        status: 'pending',
      },
    };
    const {
      files,
      externalFiles,
      uploadedFiles,
    } = useFiles();

    files.value.push(uploadedFile, externalFile);

    expect(externalFiles.value).toHaveLength(1);
    expect(externalFiles.value).toContainEqual(externalFile);

    expect(uploadedFiles.value).toHaveLength(1);
    expect(uploadedFiles.value).toContainEqual(uploadedFile);
  });

  it('saves and deletes files', async () => {
    const {
      files,
      saveFiles,
    } = useFiles({ preview: true });

    files.value = [
      <UploadedFileResource>{
        id: '',
        type: 'files',
        attributes: {
          name: 'uploadedFile.pdf',
          mimetype: 'application/pdf',
          size: 1337,
          tempPath: 'tmp/123-456-789',
        },
      },
      <ExternalFileResource>{
        id: '',
        type: 'files',
        attributes: {
          name: 'externalFile.jpg',
          url: 'http://vorgaenge.demv.internal/some/file/path',
          origin: 'aktuellerVorgang',
        },
      },
    ];

    const postSpy = vi.spyOn(api, 'post');
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    postSpy.mockResolvedValueOnce({
      data: {
        data: {
          id: '1',
          type: 'files',
          attributes: {
            name: 'uploadedFile.pdf',
            mimetype: 'application/pdf',
            size: 1337,
          },
        },
      },
    });
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    postSpy.mockResolvedValueOnce({
      data: {
        data: {
          id: '2',
          type: 'files',
          attributes: {
            name: 'externalFile.jpg',
            mimetype: null,
            size: null,
          },
        },
      },
    });

    const delSpy = vi.spyOn(api, 'del');
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    delSpy.mockResolvedValue(null);

    await saveFiles('1234', 'korrespondenzen');

    expect(postSpy).toHaveBeenCalledTimes(2);
    expect(delSpy).toHaveBeenCalledTimes(0);
    expect(files.value.map(({ id }) => id)).toEqual(['1', '2']);

    await saveFiles('1234', 'korrespondenzen');

    expect(postSpy).toHaveBeenCalledTimes(2);
    expect(delSpy).toHaveBeenCalledTimes(0);

    files.value.splice(1, 1);

    await saveFiles('1234', 'korrespondenzen');

    expect(postSpy).toHaveBeenCalledTimes(2);
    expect(delSpy).toHaveBeenCalledTimes(1);
    expect(files.value.map(({ id }) => id)).toEqual(['1']);

    files.value = [];

    await saveFiles('1234', 'korrespondenzen');

    expect(postSpy).toHaveBeenCalledTimes(2);
    expect(delSpy).toHaveBeenCalledTimes(2);
    expect(files.value).toHaveLength(0);
  });

  it('clears savedFileIds', async () => {
    const {
      files,
      saveFiles,
      clearSavedFileIds,
    } = useFiles({ preview: true });

    files.value.push({
      id: '',
      type: 'files',
      attributes: {
        name: 'uploadedFile.pdf',
        mimetype: 'application/pdf',
        size: 1337,
        tempPath: 'tmp/123-456-789',
        status: 'pending',
      },
    });

    const postSpy = vi.spyOn(api, 'post');
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    postSpy.mockResolvedValue({
      data: {
        data: {
          id: '1',
          type: 'files',
          attributes: {
            name: 'uploadedFile.pdf',
            mimetype: 'application/pdf',
            size: 1337,
            status: 'pending',
          },
        },
      },
    });

    await saveFiles('1234', 'korrespondenzen');

    expect(postSpy).toHaveBeenCalledTimes(1);

    await saveFiles('1234', 'korrespondenzen');

    expect(postSpy).toHaveBeenCalledTimes(1);

    clearSavedFileIds();

    await saveFiles('1234', 'korrespondenzen');

    expect(postSpy).toHaveBeenCalledTimes(2);

    clearSavedFileIds();

    files.value = [];

    const delSpy = vi.spyOn(api, 'del');
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    delSpy.mockResolvedValue(null);

    await saveFiles('1234', 'korrespondenzen');

    expect(delSpy).toHaveBeenCalledTimes(0);
  });
});
