import { BasicVorgangResource, Vorgangsart } from '@/store/resources/types';
import { VorgangStatus } from '@/types';

export const basicVorgangResource: BasicVorgangResource = {
  type: 'vorgaenge',
  id: '201',
  attributes: {
    titel: 'Aktualisierung Maklerauftrag (M-K)',
    status: VorgangStatus.Offen,
    vorgangsart: Vorgangsart.Aufgabe,
    isWichtig: false,
    vorgangsnummer: 'DEM-133337',
    faelligAt: '2021-09-08',
    isGroup: false,
    isUntervorgang: false,
    createdAt: '2021-09-06T11:48:13+00:00',
    updatedAt: '2021-09-07T14:42:19+00:00',
  },

  links: {
    self: 'http://vorgaenge.demv.internal/api/vorgaenge/201',
  },

  relationships: {
    timeline: {
      data: [
        {
          type: 'timelineEintraege',
          id: '501',
        },
        {
          type: 'timelineEintraege',
          id: '502',
        },
      ],
    },

    verknuepfungen: {
      data: [],
    },

    kunde: {
      data: null,
    },

    gesellschaft: {
      data: null,
    },

    vertraege: {
      data: [],
    },

    participants: {
      data: [
        {
          type: 'participants',
          id: '101',
        },
      ],
    },
  },
};
