import axios, { AxiosResponse } from 'axios';
import { merge } from 'lodash-es';

import { del, isAxiosError, post } from '@/api';
import { eventBus } from '@/store/resources/store';
import { ExternalFileResource, FileResource, UploadedFileResource } from '@/store/resources/types';
import { Error as JsonApiError } from '@/types/jsonapi';

type SignedStorageUrlResponse = {
  path: string,
  url: string,
  headers: Record<string, string>,
};

type ToastEvent =
  | 'error'
  | 'mailConverterMsgFailed'
  | 'mailConverterContentFailed'
  | 'mailConverterFileFailed';

const ERROR_CODE_TO_TOAST_EVENT: Record<string, ToastEvent> = {
  'MAIL_CONVERTER_MSG_FAILED': 'mailConverterMsgFailed',
  'MAIL_CONVERTER_CONTENT_FAILED': 'mailConverterContentFailed',
  'MAIL_CONVERTER_FILE_FAILED': 'mailConverterFileFailed',
};

/**
 * @param error
 * @param fallbackEvent
 */
export const emitToastForError = (error: unknown, fallbackEvent: string = 'error'): void => {
  if (!isAxiosError(error) || !error.response?.data) {
    eventBus.emit(fallbackEvent);

    return;
  }

  const errors = error.response.data.errors;
  if (!Array.isArray(errors) || errors.length === 0) {
    eventBus.emit(fallbackEvent);

    return;
  }

  const firstError = errors[0] as JsonApiError;
  const errorCode = firstError.code;

  const toastEvent =
    (errorCode && ERROR_CODE_TO_TOAST_EVENT[errorCode]) ?? fallbackEvent;

  eventBus.emit(toastEvent);
};

/**
 * Upload a file to S3 and return the temp path
 */
export async function uploadFileToS3(file: File): Promise<string> {
  try {
    const response: AxiosResponse<SignedStorageUrlResponse> = await axios.post(
      '/api/signed-storage-url',
      {
        'Content-Type': 'application/json',
      },
    );

    const { headers } = response.data;
    if ('Host' in headers) {
      delete headers.Host;
    }

    await axios.put(response.data.url, file, {
      headers,
    });

    return response.data.path;
  } catch (e) {
    eventBus.emit('error');

    return '';
  }
}

export async function saveFile<T extends UploadedFileResource | ExternalFileResource>(
  file: T,
  ownerId: string,
  ownerType: string,
  sync: boolean = false, // if true, the file will be saved synchronously
): Promise<T> {
  try {
    const response = await post<FileResource>('files/', {
      sync,
      data: merge(file, {
        relationships: {
          owner: {
            data: {
              id: ownerId,
              type: ownerType,
            },
          },
        },
      }),
    });

    const newFile = response?.data?.data;

    if (newFile === undefined) {
      return file;
    }

    file.id = newFile.id;
    file.links = newFile.links;

    return file;
  } catch (e) {
    emitToastForError(e);

    if (!isAxiosError(e) || e.response === undefined) {
      throw e;
    }
  }

  return file;
}

export async function deleteFile(id: string): Promise<boolean> {
  try {
    await del<FileResource>(`files/${id}`);

    return true;
  } catch (e) {
    emitToastForError(e);

    if (!isAxiosError(e) || e.response === undefined) {
      throw e;
    }

    return false;
  }
}

export function toFileResource(
  { name, type: mimetype, size }: File,
  hidden: boolean = false,
): FileResource {
  return {
    type: 'files',
    id: '',
    attributes: {
      name,
      mimetype,
      size,
      status: 'pending',
      hidden,
    },
  };
}

export function toUploadedFileResource(
  file: File,
  tempPath: string,
  hidden?: boolean,
): UploadedFileResource {
  return merge(toFileResource(file, hidden), {
    attributes: {
      tempPath,
    },
  });
}
