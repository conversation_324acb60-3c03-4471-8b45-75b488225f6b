<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\AnsprechpartnerGetRequest;
use App\Models\Gesellschaft;
use App\Models\Sparte;
use App\Models\User;
use App\Models\VorgangTyp;
use App\Services\AnsprechpartnerService;
use App\Support\DataTransferObjectCollection;
use App\Support\JsonApi\Resources\JsonApiCollection;
use Demv\ProfessionalworksSdk\Endpoints\Gesellschaft\Config\AnsprechpartnerConfig;

class AnsprechpartnerController extends Controller
{
    public function index(
        AnsprechpartnerGetRequest $request,
        Gesellschaft $gesellschaft,
        AnsprechpartnerService $ansprechpartnerService,
    ): JsonApiCollection {
        $filterArray = $request->filters();
        $spartenId = null;
        $subjectId = null;

        abort_if(!( $request->user() instanceof User), 403, 'Benutzer nicht gefunden');
        $userId = $request->user()->external_id;

        if (isset($filterArray['user'])) {
            $user = User::query()->find((int) $filterArray['user']);

            if ($user instanceof User) {
                $userId = $user->external_id;
            }
        }

        if (isset($filterArray['vorgangstyp'])) {
            $typ = VorgangTyp::query()->find((int) $filterArray['vorgangstyp']);

            if ($typ instanceof VorgangTyp) {
                $subjectId = $typ->external_support_assignment_subject_id;
            }
        }

        if (isset($filterArray['sparte'])) {
            $sparte = Sparte::query()->find((int) $filterArray['sparte']);

            if ($sparte instanceof Sparte) {
                $spartenId = $sparte->id;
            }
        }

        $config = new AnsprechpartnerConfig(
            userId: $userId,
            insuranceCompanyId: $gesellschaft->external_id,
            subjectId: $subjectId,
            forProductComboId: $spartenId,
            multiple: false,
        );

        return new DataTransferObjectCollection($ansprechpartnerService->getAnsprechpartner($config));
    }
}
